import { identifyIssues, IdentifyIssuesInternalResponse } from "./identifyIssues";
import { UserContext } from "../UserContext";
import { RenderedPromptTemplate, renderPromptTemplate, TemplateOverrides } from "../prompt-template/PromptTemplate";
import { EfficiencyReportData, Period } from "../EfficiencyReportData";
import { HoldingId, SystemHoldingId } from "../HoldingId";
import { IdentifyIssuesResponse } from "./IdentifyIssuesResponseSchema";
import { getSources } from "../source-docs/SourceDoc";
import { EffReportSummaryResponseChunk, internalChunk2CustomerChunk } from "./EffReportSummaryResponseChunk";
import { StreamedResponseWithIdChunk } from "./StreamedResponseWithIdChunk";
import { PromptCustomization } from "../PromptCustomization";
import { match } from "ts-pattern";
import { cachePromise } from "../Cache";
import logger from "../logger";
import { PIGBOT_WORKER_QUEUE, startOrAwaitWorkflow } from "../temporal/temporal";
import { EFFICIENCY_REPORT_TYPE } from "../Constants";
import { FarmInfo, GetFarmInfo } from "../cf-link/GetFarmInfo";
import { sql } from "../database";
import { EffReportPrompts } from "./EffReportPrompts";
import { FarmHoldingId } from "../FarmId";
import { getSettings } from "../VFASettings";
import { getRelevantData, GetRelevantDataResponse } from "../relevant-data/RelevantDataService";
import { getIntroduction } from "./introduction";
import { analyzeCauses, AnalyzeCausesInternalResponse } from "./analyzeCauses";
import { getLanguageLevelsContext } from "../language-level/getLanguageLevelsContext";
import { emptyUsage, streamCompletion, sumUsages, Usage } from "../llm/LlmCommon";
import { detectConflictingGoals } from "../data-quality/DataQualityService";
import { SOPLinkFormatPrompt } from "../vfamain/SOPLink";
import { getPeriodsContext } from "../period-names/PeriodsContext";

/**
 * Chunks of streamed response that is shown on prompt management screen.
 * It includes internal details to help tweak the prompts.
 */
export type EffReportInternalChunk =
	| StreamedResponseWithIdChunk
	| {
			type: "identifyIssuesResponse";
			identifyIssuesResponse: IdentifyIssuesInternalResponse;
	  }
	| {
			type: "analyzeCausesResponse";
			analyzeCausesResponse: AnalyzeCausesInternalResponse;
	  }
	| {
			type: "summarizationMetadata";
			prompt: RenderedPromptTemplate;
	  }
	| {
			type: "introduction";
			prompt: RenderedPromptTemplate;
	  }
	| {
			type: "getRelevantData";
			relevantDataResponse: GetRelevantDataResponse;
	  }
	| {
			type: "farmInfo";
			farmInfo: FarmInfo;
	  }
	| {
			type: "conflictingGoals";
			conflictingGoals: string[] | null;
	  };

export type GetEfficiencyReportSummary = {
	report: EfficiencyReportData;
	idx: number;
};

interface AnalyzeEfficiencyReportParams {
	request: GetEfficiencyReportSummary;
	templateOverrides: TemplateOverrides;
	farmHoldingId: FarmHoldingId;
	targetLanguage?: string;
}

/**
 * This function streams full internal data of efficiency report summary.
 * It caches the data in redis for 24 hours so they can be used later for feedback or pdf download.
 * Usage:
 * - Internal prompt management page.
 * - It is called by getEfficiencyReportSummary which strips internal data from the stream.
 */
export async function* getEfficiencyReportSummaryInternal(params: AnalyzeEfficiencyReportParams) {
	const stream = analyzeEfficiencyReport(params);

	const reqResData: EffReportSummaryReqResData = {
		version: "vfa-v3",
		internalResponseStreamArray: [] as EffReportInternalChunk[],
		reportData: params.request.report,
	};

	let responseId: string | undefined;

	for await (const chunk of stream) {
		// Collect all the data for the cache
		reqResData.internalResponseStreamArray.push(chunk);

		// Match the chunk to get the responseId
		match(chunk).with({ type: "responseId" }, (value) => {
			responseId = value.responseId;
		});

		yield chunk;
	}

	if (responseId) {
		// Store data temporarily in redis.
		// If a user submits feedback, then data is loaded from redis and stored in the db with the feedback.
		const cache = await cachePromise;
		await cache.set(responseId, reqResData, 24 * 60 * 60 * 1000); // cache for a day
	} else {
		logger.error("No responseId found in the stream");
	}
}

/**
 * This function streams full internal data of efficiency report summary.
 */
export async function* analyzeEfficiencyReport({
	request,
	templateOverrides,
	farmHoldingId,
	targetLanguage,
}: AnalyzeEfficiencyReportParams): AsyncGenerator<EffReportInternalChunk> {
	const promptCustomization = {
		templateOverrides,
		cacheIdx: request.idx,
	};

	const relevantPeriods = request.report.periods.slice(0, -1); // The last period is summary period, so we ignore it

	const langCode = targetLanguage ?? request.report.language;

	const farmInfo = await getFarmInfo(farmHoldingId, relevantPeriods, langCode);

	yield {
		type: "farmInfo",
		farmInfo,
	};

	const introductionResponse = await getIntroduction({
		language: langCode,
		holdingId: farmHoldingId,
		farmInfo,
		templateOverrides,
	});

	const periodsContext = getPeriodsContext(request.report.periods.slice(0, -1), langCode, farmInfo.dateFormat);

	yield {
		type: "introduction",
		prompt: introductionResponse,
	};

	// Run these two in parallel to speed up analysis
	const [conflictingGoals, identifyIssuesResponse] = await Promise.all([
		detectConflictingGoals(farmInfo.kpiGoals, farmHoldingId, introductionResponse, promptCustomization),
		identifyIssues({
			introductionResponse,
			report: request.report,
			holdingId: farmHoldingId,
			promptCustomization,
			periodsContext,
		}),
	]);

	yield {
		type: "conflictingGoals",
		conflictingGoals: conflictingGoals.response,
	};

	yield {
		type: "identifyIssuesResponse",
		identifyIssuesResponse,
	};

	const indicatingKPIs = identifyIssuesResponse.response.issues.flatMap((issue) => issue.indicatingKPIs.map((kpi) => kpi.code));
	const allKPIs = request.report.sections.flatMap((section) => section.kpis.map((kpi) => kpi.code));

	const relevantDataResponse = await getRelevantData({
		farmHoldingId: farmHoldingId,
		langCode: langCode,
		periods: relevantPeriods,
		indicatingKPIs,
		reportKpis: allKPIs,
		breeds: request.report.reportContext?.breeds ?? null,
	});

	yield {
		type: "getRelevantData",
		relevantDataResponse,
	};

	const causesResponses = await analyzeCauses({
		report: request.report,
		holdingId: farmHoldingId,
		introductionResponse,
		promptCustomization,
		relevantDataResponse,
		issuesResponse: identifyIssuesResponse.response,
		periodsContext,
	});

	yield {
		type: "analyzeCausesResponse",
		analyzeCausesResponse: causesResponses,
	};

	for await (const chunk of streamSummary({
		identifiedIssues: identifyIssuesResponse.response,
		report: request.report,
		causesResponses,
		farmHoldingId,
		introductionResponse,
		promptCustomization,
	})) {
		// ChatGpt returns data that needs to be processed before yielding
		if (typeof chunk === "string") {
			yield chunk;
		} else if (chunk.type === "responseHash") {
			yield {
				type: "responseId",
				responseId: chunk.hash,
			} as const;
		} else if (chunk.type === "usage") {
			const sumUsage = [chunk, identifyIssuesResponse.usage, conflictingGoals.usage, ...causesResponses.map((cr) => cr.usage), chunk]
				.filter((usage) => usage !== undefined)
				.reduce((acc: Usage, usage: Usage) => sumUsages(acc, usage), emptyUsage({ activity: "eff-report", action: "sum" }));

			yield sumUsage;
		} else {
			yield chunk;
		}
	}
}

export async function getFarmInfo(farmHoldingId: FarmHoldingId, relevantPeriods: Period[], langCode: string) {
	const settings = await getSettings(farmHoldingId);
	return await startOrAwaitWorkflow(
		PIGBOT_WORKER_QUEUE,
		`getFarmInfo-${farmHoldingId.farmId}-${farmHoldingId.env}`,
		GetFarmInfo,
		[farmHoldingId, settings.useSegesManual, relevantPeriods, langCode],
		"3m",
	);
}

// TODO Only require relevant parts of the parameters, not full objects
async function* streamSummary(params: {
	report: EfficiencyReportData;
	causesResponses: AnalyzeCausesInternalResponse;
	farmHoldingId: HoldingId;
	identifiedIssues: IdentifyIssuesResponse;
	introductionResponse: RenderedPromptTemplate;
	promptCustomization: PromptCustomization;
}) {
	const relevantFor = Array.from(new Set(params.identifiedIssues.issues.flatMap((issue) => issue.indicatingKPIs.map((kpi) => kpi.code))));

	const sources = await getSources({ userLanguage: params.report.language, holdingId: params.farmHoldingId, relevantFor });

	const holdingInstructions = (
		await renderPromptTemplate({
			promptId: {
				promptType: EffReportPrompts.summary_holdingInstructions,
				holdingId: params.farmHoldingId,
				templateOverrides: params.promptCustomization.templateOverrides,
			},
			optional: true,
		})
	)?.template.template;

	const analyzedIssues = params.causesResponses.map((cause, index) => ({
		issue: cause.issue,
		cause: cause.cause,
		analysis: cause.analysis,
		letter: String.fromCharCode(65 + index),
	}));

	const SOPsExist = Object.values(sources as Record<string, string | undefined>).some((sops) => sops !== undefined);

	const prompt = await renderPromptTemplate({
		promptId: {
			promptType: EffReportPrompts.summary,
			holdingId: SystemHoldingId,
			templateOverrides: params.promptCustomization.templateOverrides,
		},
		context: {
			...params.introductionResponse.context,
			...sources,
			analyzedIssues,
			holdingInstructions,
			SOPsExist,
			SOPLinkFormatPrompt: SOPLinkFormatPrompt,
			...(await getLanguageLevelsContext(params.promptCustomization.templateOverrides)),
		},
	});

	yield {
		type: "summarizationMetadata" as const,
		prompt,
	};

	yield* streamCompletion({
		messages: [
			{
				role: "system",
				content: params.introductionResponse.content,
			},
			{
				role: "user",
				content: prompt.content,
			},
		],
		cacheIdx: params.promptCustomization.cacheIdx,
		metricData: {
			activity: "eff-report",
			action: "summarize",
		},
	});
}

/**
 * Request and response data for eff report summary.
 * These data are first stored in redis, and if a user submits feedback, they are stored in the db.
 */
export type EffReportSummaryReqResData = {
	version: "vfa-v3" | "vfa-v2";
	internalResponseStreamArray: EffReportInternalChunk[];
	reportData: EfficiencyReportData;
};

/**
 * This function streams only what is needed for the user to see. It is used on the report page.
 */
export async function* getEfficiencyReportSummary(
	request: GetEfficiencyReportSummary,
	userContext: UserContext,
): AsyncGenerator<EffReportSummaryResponseChunk> {
	const internalResponse = getEfficiencyReportSummaryInternal({ request: request, templateOverrides: {}, farmHoldingId: userContext });

	async function insertRequest() {
		try {
			await sql`
				insert into report_request
					(type, farm_id, root_id, farm_name, report_data, created_by, country)
				values (${EFFICIENCY_REPORT_TYPE}, ${userContext.farmId}, ${userContext.rootId}, ${userContext.farmName},
								${sql.json(request.report)}, ${userContext.userId}, ${userContext.countryCode})`;
		} catch (e) {
			logger.error(e);
		}
	}

	async function deleteRequest() {
		try {
			await sql`delete
								from report_request
								where type = ${EFFICIENCY_REPORT_TYPE}
									and farm_id = ${userContext.farmId}
									-- ignores language region - if 'en' or 'en_US' is in reportResponse.language, every entry with language 'en' AND 'en_US' is removed.
									and (report_data ->> 'language' like ${request.report.language} || '%' or
											 ${request.report.language} like report_data ->> 'language' || '%')`;
		} catch (e) {
			logger.error(e);
		}
	}

	await deleteRequest();
	await insertRequest();

	for await (const chunk of internalResponse) {
		// Collect only the data needed for the customer
		const customerChunks = internalChunk2CustomerChunk(chunk, userContext.role);
		yield* customerChunks;
	}
}
