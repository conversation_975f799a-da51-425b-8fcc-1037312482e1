import { Period } from "../EfficiencyReportData";
import moment from "moment";

export type PeriodNaming = {
	code: string; // `${from}–${to}`
	from: string;
	to: string;
	label: string; // renamed from graphItemLabel
	labelDesc: string | undefined; // renamed from graphDescLabel
	textName: string;
};

/**
 * Get month names for a given language code using moment.js
 */
function getMonthNames(langCode: string, format: "long" | "short" = "long"): string[] {
	// Set moment locale
	const originalLocale = moment.locale();
	moment.locale(langCode);

	const monthList = [...Array(12).keys()]; // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]

	const getMonthName = (monthIndex: number) => {
		const monthMoment = moment().month(monthIndex);
		return format === "short" ? monthMoment.format("MMM") : monthMoment.format("MMMM");
	};

	const result = monthList.map(getMonthName);

	// Restore original locale
	moment.locale(originalLocale);

	return result;
}

/**
 * Format a date using the given language code and custom format (short format for graphItemLabel)
 */
function formatDateByLanguage(dateStr: string, langCode: string, dateFormat: string, showYear: boolean = true): string {
	try {
		const dateMoment = moment(dateStr);

		if (!dateMoment.isValid()) {
			return dateStr;
		}

		if (dateFormat === "t") {
			// Use moment to format dates with thousand-days calendar format
			const localizedMoment = dateMoment.clone().locale(langCode);
			return localizedMoment.format("t");
		} else {
			// Use Intl.DateTimeFormat for short format
			const options: Intl.DateTimeFormatOptions = {
				month: "short",
				day: "numeric",
			};

			if (showYear) {
				options.year = "numeric";
			}

			const formatter = new Intl.DateTimeFormat(langCode, options);
			return formatter.format(dateMoment.toDate());
		}
	} catch (error) {
		return dateStr;
	}
}

/**
 * Format a date using the given language code and custom format (long format for textName)
 */
function formatDateByLanguageLong(dateStr: string, langCode: string, dateFormat: string, showYear: boolean = true): string {
	try {
		const dateMoment = moment(dateStr);

		if (!dateMoment.isValid()) {
			return dateStr;
		}

		if (dateFormat === "t") {
			// Use moment to format dates with thousand-days calendar format
			const localizedMoment = dateMoment.clone().locale(langCode);
			return localizedMoment.format("t");
		} else {
			// Use Intl.DateTimeFormat for long format
			const options: Intl.DateTimeFormatOptions = {
				month: "long",
				day: "numeric",
			};

			if (showYear) {
				options.year = "numeric";
			}

			const formatter = new Intl.DateTimeFormat(langCode, options);
			return formatter.format(dateMoment.toDate());
		}
	} catch (error) {
		return dateStr;
	}
}

/**
 * Format a date range with smart year handling - if both dates need years and are in the same year,
 * only show the year once at the end
 */
function formatDateRangeByLanguage(fromDateStr: string, toDateStr: string, langCode: string, dateFormat: string, showYear: boolean = true): string {
	try {
		const fromMoment = moment(fromDateStr);
		const toMoment = moment(toDateStr);

		if (!fromMoment.isValid() || !toMoment.isValid()) {
			return `${fromDateStr}–${toDateStr}`;
		}

		if (dateFormat === "t") {
			// For thousand-days calendar, format each date separately
			const fromFormatted = formatDateByLanguage(fromDateStr, langCode, dateFormat, showYear);
			const toFormatted = formatDateByLanguage(toDateStr, langCode, dateFormat, showYear);
			return `${fromFormatted}–${toFormatted}`;
		} else {
			// Check if both dates are in the same year and we need to show years
			const sameYear = fromMoment.year() === toMoment.year();

			if (showYear && sameYear) {
				// Format dates without year, then add year once at the end
				const fromFormatted = formatDateByLanguage(fromDateStr, langCode, dateFormat, false);
				const toFormatted = formatDateByLanguage(toDateStr, langCode, dateFormat, false);
				const year = fromMoment.year();
				return `${fromFormatted}–${toFormatted}, ${year}`;
			} else {
				// Format each date separately (either no year needed, or different years)
				const fromFormatted = formatDateByLanguage(fromDateStr, langCode, dateFormat, showYear);
				const toFormatted = formatDateByLanguage(toDateStr, langCode, dateFormat, showYear);
				return `${fromFormatted}–${toFormatted}`;
			}
		}
	} catch (error) {
		return `${fromDateStr}–${toDateStr}`;
	}
}

/**
 * Calculate the number of days between two ISO date strings using moment.js
 */
function calculateDaysBetween(from: string, to: string): number {
	const fromMoment = moment(from);
	const toMoment = moment(to);
	return toMoment.diff(fromMoment, "days") + 1; // +1 to include both start and end dates
}

/**
 * Check if a period spans exactly one calendar month using moment.js
 */
function isCalendarMonth(from: string, to: string): boolean {
	const fromMoment = moment(from);
	const toMoment = moment(to);

	// Check if from is the first day of the month and to is the last day of the same month
	const startOfMonth = fromMoment.clone().startOf("month");
	const endOfMonth = fromMoment.clone().endOf("month");

	return fromMoment.isSame(startOfMonth, "day") && toMoment.isSame(endOfMonth, "day") && fromMoment.isSame(toMoment, "month");
}

/**
 * Check if a period spans a range of calendar months using moment.js
 */
function isCalendarMonthRange(from: string, to: string): boolean {
	const fromMoment = moment(from);
	const toMoment = moment(to);

	// Check if from is the first day of a month
	const startOfFromMonth = fromMoment.clone().startOf("month");
	if (!fromMoment.isSame(startOfFromMonth, "day")) {
		return false;
	}

	// Check if to is the last day of a month
	const endOfToMonth = toMoment.clone().endOf("month");
	if (!toMoment.isSame(endOfToMonth, "day")) {
		return false;
	}

	// Check if it spans multiple months
	return !fromMoment.isSame(toMoment, "month");
}

/**
 * Determine the type of periods and generate appropriate naming
 */
export function getPeriodNamings(periods: Period[], langCode: string, dateFormat: string): PeriodNaming[] {
	if (periods.length === 0) {
		return [];
	}

	// Determine period type by checking the first period
	const firstPeriod = periods[0];

	if (isCalendarMonth(firstPeriod.from, firstPeriod.to)) {
		// Type A: Calendar month periods
		const shortMonthNames = getMonthNames(langCode, "short");
		const longMonthNames = getMonthNames(langCode, "long");

		return periods.map((period) => {
			const fromMoment = moment(period.from);
			const shortMonthName = shortMonthNames[fromMoment.month()];
			const longMonthName = longMonthNames[fromMoment.month()];

			return {
				code: `${period.from}–${period.to}`,
				from: period.from,
				to: period.to,
				label: shortMonthName,
				labelDesc: undefined,
				textName: longMonthName,
			};
		});
	} else if (isCalendarMonthRange(firstPeriod.from, firstPeriod.to)) {
		// Type B: Range of calendar months periods
		const shortMonthNames = getMonthNames(langCode, "short");
		const longMonthNames = getMonthNames(langCode, "long");

		return periods.map((period) => {
			const fromMoment = moment(period.from);
			const toMoment = moment(period.to);

			const firstShortMonth = shortMonthNames[fromMoment.month()];
			const lastShortMonth = shortMonthNames[toMoment.month()];
			const shortLabel = `${firstShortMonth}–${lastShortMonth}`;

			const firstLongMonth = longMonthNames[fromMoment.month()];
			const lastLongMonth = longMonthNames[toMoment.month()];
			const longLabel = `${firstLongMonth}–${lastLongMonth}`;

			return {
				code: `${period.from}–${period.to}`,
				from: period.from,
				to: period.to,
				label: shortLabel,
				labelDesc: undefined,
				textName: longLabel,
			};
		});
	} else {
		// Type C: Same length periods
		const periodLength = calculateDaysBetween(firstPeriod.from, firstPeriod.to);

		// Determine period description (plural form for graphDescLabel, singular for textName)
		const periodDescSingular = periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
		const periodDescPlural = periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
		const periodDescWithLabelPlural = `${periodDescPlural} periods`;
		const periodDescWithLabelSingular = `${periodDescSingular} period`;

		// Find the latest period to determine if we need to show years
		const latestPeriodMoment = moment.max(periods.map((p) => moment(p.to)));

		return periods.map((period, index) => {
			const periodMoment = moment(period.to);
			const monthsDiff = latestPeriodMoment.diff(periodMoment, "months");
			const showYear = monthsDiff >= 11;

			const startDateShort = formatDateByLanguage(period.from, langCode, dateFormat, showYear);
			const endDateShort = formatDateByLanguage(period.to, langCode, dateFormat, showYear);
			const endDateLong = formatDateByLanguageLong(period.to, langCode, dateFormat, showYear);
			// Check if this period is the latest chronologically, not by array position
			const isLatestPeriod = periodMoment.isSame(latestPeriodMoment, "day");

			const textName = isLatestPeriod ? `Latest ${periodDescWithLabelSingular}` : `${periodDescWithLabelSingular} ending ${endDateLong}`;

			// For same length periods (Type C), use from–to format for label
			const label = `${startDateShort}–${endDateShort}`;

			return {
				code: `${period.from}–${period.to}`,
				from: period.from,
				to: period.to,
				label: label,
				labelDesc: periodDescWithLabelPlural,
				textName: textName,
			};
		});
	}
}
