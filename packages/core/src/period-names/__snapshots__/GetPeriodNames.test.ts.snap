// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`getPeriodNamingsPrompt should return prompt with period names 1`] = `
"Data were calculated for the following periods:
[
  {
    "code": "2024-01-01–2024-01-31",
    "from": "2024-01-01",
    "to": "2024-01-31",
    "label": "Jan",
    "textName": "January"
  },
  {
    "code": "2024-02-01–2024-02-29",
    "from": "2024-02-01",
    "to": "2024-02-29",
    "label": "Feb",
    "textName": "February"
  }
]

Field descriptions:
"textName": Use this name when referring to the period in your response text.
"label": Use this name when referring to the period in a chart or table.
"labelDesc": Use this to explain period labels in a chart or table. If it is undefined, don't explain period labels.
"code": Internal code for the period. It will be used when referring to the period in the data. Never write the code in your response as it is only internal.
"from": Start date of the period in ISO format.
"to": End date of the period in ISO format."
`;
