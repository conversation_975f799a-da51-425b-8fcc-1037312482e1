import { Period } from "../EfficiencyReportData";
import { renderTemplateFile } from "../utils/renderTemplateFile";
import path from "node:path";
import { getPeriodNamings, PeriodNaming } from "./GetPeriodNames";

export type PeriodCodes = { [k: number]: string };

export function getPeriodsContext(periods: Period[], langCode: string, dateFormat: string) {
	const periodNamings = getPeriodNamings(periods, langCode, dateFormat);
	const periodCodes: PeriodCodes = Object.fromEntries(periodNamings.map((periodNaming, idx) => [idx, periodNaming.code]));

	return {
		periodNamings,
		periodCodes,
		latestPeriodName: periodNamings[0].textName,
		periodNamingsPrompt: getPeriodNamingsPrompt(periodNamings),
	};
}

export type PeriodsContext = ReturnType<typeof getPeriodsContext>;

export function getPeriodNamingsPrompt(periodNamings: PeriodNaming[]) {
	return renderTemplateFile(path.resolve(__dirname, "period-naming.hbs"), { periodNamings: JSON.stringify(periodNamings, null, 2) });
}