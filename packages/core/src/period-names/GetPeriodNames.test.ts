import { describe, expect, test } from "@jest/globals";
import { getPeriodNamings } from "./GetPeriodNames";

describe("getPeriodNamings", () => {
	describe("Calendar month periods (Type A)", () => {
		test("should handle single calendar month periods in English", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" }, // Leap year
				{ from: "2024-03-01", to: "2024-03-31" },
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan",
					labelDesc: undefined,
					textName: "January",
				},
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
				{
					code: "2024-03-01–2024-03-31",
					from: "2024-03-01",
					to: "2024-03-31",
					label: "Mar",
					labelDesc: undefined,
					textName: "March",
				},
			]);
		});

		test("should handle calendar month periods in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" },
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan.",
					labelDesc: undefined,
					textName: "Januar",
				},
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb.",
					labelDesc: undefined,
					textName: "Februar",
				},
			]);
		});

		test("should handle calendar month periods in French", () => {
			const periods = [
				{ from: "2024-06-01", to: "2024-06-30" },
				{ from: "2024-07-01", to: "2024-07-31" },
			];

			const result = getPeriodNamings(periods, "fr", "standard");

			expect(result).toEqual([
				{
					code: "2024-06-01–2024-06-30",
					from: "2024-06-01",
					to: "2024-06-30",
					label: "juin",
					labelDesc: undefined,
					textName: "juin",
				},
				{
					code: "2024-07-01–2024-07-31",
					from: "2024-07-01",
					to: "2024-07-31",
					label: "juil.",
					labelDesc: undefined,
					textName: "juillet",
				},
			]);
		});
	});

	describe("Range of calendar months periods (Type B)", () => {
		test("should handle range of calendar months in English", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-03-31" }, // Jan-Mar
				{ from: "2024-04-01", to: "2024-06-30" }, // Apr-Jun
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-03-31",
					from: "2024-01-01",
					to: "2024-03-31",
					label: "Jan–Mar",
					labelDesc: undefined,
					textName: "January–March",
				},
				{
					code: "2024-04-01–2024-06-30",
					from: "2024-04-01",
					to: "2024-06-30",
					label: "Apr–Jun",
					labelDesc: undefined,
					textName: "April–June",
				},
			]);
		});

		test("should handle range of calendar months in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-02-29" }, // Jan-Feb
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-02-29",
					from: "2024-01-01",
					to: "2024-02-29",
					label: "Jan.–Feb.",
					labelDesc: undefined,
					textName: "Januar–Februar",
				},
			]);
		});

		test("should handle cross-year range of calendar months", () => {
			const periods = [
				{ from: "2023-11-01", to: "2024-01-31" }, // Nov-Jan
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2023-11-01–2024-01-31",
					from: "2023-11-01",
					to: "2024-01-31",
					label: "Nov–Jan",
					labelDesc: undefined,
					textName: "November–January",
				},
			]);
		});
	});

	describe("Same length periods (Type C)", () => {
		test("should handle weekly periods", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
				{ from: "2024-01-15", to: "2024-01-21" }, // 7 days
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 7",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7",
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "Jan 14",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 14",
				},
				{
					code: "2024-01-15–2024-01-21",
					from: "2024-01-15",
					to: "2024-01-21",
					label: "Jan 21",
					labelDesc: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});

		test("should handle 4-week periods", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-28" }, // 28 days = 4 weeks
				{ from: "2024-01-29", to: "2024-02-25" }, // 28 days = 4 weeks
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-28",
					from: "2024-01-01",
					to: "2024-01-28",
					label: "Jan 28",
					labelDesc: "4-week periods",
					textName: "4-week period ending January 28",
				},
				{
					code: "2024-01-29–2024-02-25",
					from: "2024-01-29",
					to: "2024-02-25",
					label: "Feb 25",
					labelDesc: "4-week periods",
					textName: "Latest 4-week period",
				},
			]);
		});

		test("should handle non-weekly periods (days)", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-20" }, // 20 days
				{ from: "2024-01-21", to: "2024-02-09" }, // 20 days
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-20",
					from: "2024-01-01",
					to: "2024-01-20",
					label: "Jan 20",
					labelDesc: "20-day periods",
					textName: "20-day period ending January 20",
				},
				{
					code: "2024-01-21–2024-02-09",
					from: "2024-01-21",
					to: "2024-02-09",
					label: "Feb 9",
					labelDesc: "20-day periods",
					textName: "Latest 20-day period",
				},
			]);
		});

		test("should handle same length periods in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "7. Jan.",
					labelDesc: "1-week periods",
					textName: "1-week period ending 7. Januar",
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "14. Jan.",
					labelDesc: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});
	});

	describe("Edge cases", () => {
		test("should handle empty periods array", () => {
			const result = getPeriodNamings([], "en", "standard");
			expect(result).toEqual([]);
		});

		test("should handle single period", () => {
			const periods = [{ from: "2024-01-01", to: "2024-01-31" }];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan",
					labelDesc: undefined,
					textName: "January",
				},
			]);
		});

		test("should handle invalid language code gracefully", () => {
			const periods = [{ from: "2024-01-01", to: "2024-01-31" }];

			// Should not throw an error, but use fallback behavior
			const result = getPeriodNamings(periods, "invalid-lang", "standard");
			expect(result).toHaveLength(1);
			expect(result[0].label).toBeDefined();
		});

		test("should handle leap year February correctly", () => {
			const periods = [
				{ from: "2024-02-01", to: "2024-02-29" }, // Leap year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
			]);
		});

		test("should handle non-leap year February correctly", () => {
			const periods = [
				{ from: "2023-02-01", to: "2023-02-28" }, // Non-leap year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2023-02-01–2023-02-28",
					from: "2023-02-01",
					to: "2023-02-28",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
			]);
		});
	});

	describe("Period type detection", () => {
		test("should not treat partial month as calendar month", () => {
			const periods = [
				{ from: "2024-01-02", to: "2024-01-31" }, // Not starting from 1st
			];

			const result = getPeriodNamings(periods, "en", "standard");

			// Should be treated as same-length period, not calendar month
			expect(result[0].labelDesc).toBeDefined(); // Should have period description
			expect(result[0].textName).toContain("Latest");
		});

		test("should not treat partial month end as calendar month", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-30" }, // Not ending on last day
			];

			const result = getPeriodNamings(periods, "en", "standard");

			// Should be treated as same-length period, not calendar month
			expect(result[0].labelDesc).toBeDefined(); // Should have period description
			expect(result[0].textName).toContain("Latest");
		});

		test("should show years when 11+ months difference", () => {
			const periods = [
				{ from: "2023-01-01", to: "2023-01-07" }, // 12 months before latest (should show year)
				{ from: "2023-03-01", to: "2023-03-07" }, // 10 months before latest (should not show year)
				{ from: "2024-01-01", to: "2024-01-07" }, // Latest year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2023-01-01–2023-01-07",
					from: "2023-01-01",
					to: "2023-01-07",
					label: "Jan 7, 2023", // Shows year (12 months difference >= 11)
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7, 2023",
				},
				{
					code: "2023-03-01–2023-03-07",
					from: "2023-03-01",
					to: "2023-03-07",
					label: "Mar 7", // No year (10 months difference < 11)
					labelDesc: "1-week periods",
					textName: "1-week period ending March 7",
				},
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 7", // No year (latest)
					labelDesc: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});
	});

	describe("Period ordering", () => {
		test("should correctly identify latest period when periods are in reverse order (newest to oldest)", () => {
			const periods = [
				{ from: "2024-01-15", to: "2024-01-21" }, // Latest period (newest)
				{ from: "2024-01-08", to: "2024-01-14" }, // Middle period
				{ from: "2024-01-01", to: "2024-01-07" }, // Oldest period
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-15–2024-01-21",
					from: "2024-01-15",
					to: "2024-01-21",
					label: "Jan 21",
					labelDesc: "1-week periods",
					textName: "Latest 1-week period", // Should be marked as latest
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "Jan 14",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 14", // Should NOT be marked as latest
				},
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 7",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7", // Should NOT be marked as latest
				},
			]);
		});

		test("should correctly identify latest period when periods are in chronological order (oldest to newest)", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // Oldest period
				{ from: "2024-01-08", to: "2024-01-14" }, // Middle period
				{ from: "2024-01-15", to: "2024-01-21" }, // Latest period (newest)
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 7",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7", // Should NOT be marked as latest
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "Jan 14",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 14", // Should NOT be marked as latest
				},
				{
					code: "2024-01-15–2024-01-21",
					from: "2024-01-15",
					to: "2024-01-21",
					label: "Jan 21",
					labelDesc: "1-week periods",
					textName: "Latest 1-week period", // Should be marked as latest
				},
			]);
		});
	});

	describe("Thousand-days calendar format (dateFormat: 't')", () => {
		test("should use moment format 't' for thousand-days calendar", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
			];

			const result = getPeriodNamings(periods, "en", "t");

			// The 't' format produces thousand-days calendar dates like "19-095"
			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "19-095",
					labelDesc: "1-week periods",
					textName: "1-week period ending 19-095",
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "19-102",
					labelDesc: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});

		test("should use moment format 't' for calendar months with thousand-days format", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" },
			];

			const result = getPeriodNamings(periods, "en", "t");

			// For calendar months, the thousand-days format doesn't apply to month names
			// Only to date formatting in Type C periods
			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan",
					labelDesc: undefined,
					textName: "January",
				},
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
			]);
		});
	});
});
