/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import Button from 'antd/lib/button';
import Spin from 'antd/lib/spin';
import 'antd/lib/alert/style/index.less';
import 'antd/lib/spin/style/index.less';
import React, { useEffect, useState } from 'react';
import { makeAutoObservable } from 'mobx';
import { observer } from 'mobx-react-lite';
import { PDFRenderer, PDFRendererState } from './PDFRenderer';
import { FarmLocationSelector } from './FarmSelector';
import { Pending } from '../Pending';
import { FarmAnalysis, FarmAnalysisState } from './FarmAnalysis';
import { PeriodSelector } from './PeriodSelector';
import { PeriodType, StandardPeriodLengthDays, isDatePeriod } from 'pigbot-core/src/vfamain/Periods';
import moment from 'moment';
import { getPeriodInterval } from './PeriodInterval';
import TrpcReact from '@src/common/TrpcReact';
import { SowFarmLocationsAndPeriodInfo } from 'pigbot-backend/src/vfamain/VFAMainService';
import { LoadingError } from '../common/LoadingError';
import PigbotMarkdown from '../common/PigbotMarkdown';
import { removeHeading } from 'pigbot-core/src/utils/markdownUtils';

interface Props {
	dateFormat: string;
	jwtToken: string;
}

class FarmAnalysisPageState {
	farmAnalysisState: FarmAnalysisState = new FarmAnalysisState();
	selectedFarmLocationId: number | null | Pending = Pending;
	pdfRendererState?: PDFRendererState;

	periodType: PeriodType = 'standard-period';
	// periodType to decide which one will be sent to BE
	periodSinceDate: moment.Moment = moment().subtract(7, 'days'); // default: today - 7 days
	periodSinceMonth: moment.Moment = moment().subtract(1, 'month').startOf('month'); // default: previous month

	analyze: boolean = false;

	analysisData: { index: number; title: string; relevantData: string } | null = null;

	constructor() {
		makeAutoObservable(this);
	}

	setSelectedFarmLocation(farmLocationId: number | null) {
		this.selectedFarmLocationId = farmLocationId;

		// reset state
		this.clearPdfContent();
		this.analysisData = null;
	}

	openLink(pdfDocumentName: string, pageNumber: number) {
		if (this.pdfRendererState) this.pdfRendererState.openLink(pdfDocumentName, pageNumber + 1);
		else this.pdfRendererState = new PDFRendererState(pdfDocumentName, pageNumber + 1);
	}

	clearPdfContent() {
		this.pdfRendererState = undefined;
	}

	setPeriodType(periodType: PeriodType) {
		this.periodType = periodType;
	}

	setPeriodSince(periodSince: moment.Moment) {
		if (isDatePeriod(this.periodType)) {
			this.periodSinceDate = periodSince;
		} else {
			this.periodSinceMonth = periodSince;
		}
	}

	runAnalysis() {
		this.analyze = true;
	}

	setAnalysisRelevantData(index: number, title: string, data: string) {
		this.analysisData = {
			index: index,
			title: title,
			relevantData: data,
		};
	}
}

export const FarmAnalysisPage: React.FC<Props> = observer((props) => {
	const [state] = useState(() => new FarmAnalysisPageState());

	const sowFarmLocationsAndPeriodLengthResponse = TrpcReact.getSowFarmLocationsAndPeriodLength.useQuery(undefined, { retry: false });
	const sowFarmLocationsAndPeriodLength = sowFarmLocationsAndPeriodLengthResponse.data as SowFarmLocationsAndPeriodInfo | null;
	const sowFarmLocationsAndPeriodLengthLoadedWithoutError =
		!sowFarmLocationsAndPeriodLengthResponse.isLoading && sowFarmLocationsAndPeriodLengthResponse.error === null;

	useEffect(() => {
		if (
			sowFarmLocationsAndPeriodLength &&
			sowFarmLocationsAndPeriodLength.sowFarmLocations === null &&
			state.selectedFarmLocationId === Pending
		) {
			state.setSelectedFarmLocation(null);
		}
	}, [sowFarmLocationsAndPeriodLength, state.selectedFarmLocationId]);

	const periodLength =
		sowFarmLocationsAndPeriodLength && sowFarmLocationsAndPeriodLength.periodLength
			? sowFarmLocationsAndPeriodLength.periodLength
			: StandardPeriodLengthDays;
	const periodInterval = getPeriodInterval(
		state.periodType,
		isDatePeriod(state.periodType) ? state.periodSinceDate : state.periodSinceMonth,
		periodLength,
	);

	const analysisData = state.analysisData;

	const loadingSpin = (
		<div
			css={css`
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
			`}
		>
			<Spin />
		</div>
	);

	const errorMessage = (error: string) => (
		<div
			css={css`
				display: flex;
				justify-content: center;
				align-items: center;
			`}
		>
			<LoadingError error={error} />
		</div>
	);

	const analyzeButton = (
		<div
			css={css`
				display: flex;
				justify-content: center;
				margin: 30px;
			`}
		>
			<Button disabled={state.selectedFarmLocationId === Pending} onClick={() => state.runAnalysis()} size='large' type='primary'>
				Analyze
			</Button>
		</div>
	);
	const farmAnalysis = state.selectedFarmLocationId !== Pending && (
		<FarmAnalysis
			state={state.farmAnalysisState}
			farmLocationId={state.selectedFarmLocationId}
			period={{
				periodType: state.periodType,
				periodSince: isDatePeriod(state.periodType) ? state.periodSinceDate.toDate() : state.periodSinceMonth.month() + 1, // 0 based indexing
			}}
			periodInterval={periodInterval}
			openLink={(pdfDocumentName: string, pageNumber: number) => state.openLink(pdfDocumentName, pageNumber)}
			setAnalysisRelevantData={(index, title, relevantData) => state.setAnalysisRelevantData(index, title, relevantData)}
			dateFormat={props.dateFormat}
		/>
	);

	return (
		<div
			css={css`
				display: flex;
				flex: 1;
				overflow-y: auto;
			`}
		>
			{/*
				Left margin that pushes the analysis panel to the center.
				It is using flex, instead of margin to grow/shrink with the window size.
			*/}
			<div
				css={css`
					flex: 0.05;
				`}
			/>
			{/*
				Left panel with
				- farm location selector
				- period selector
				- farm analysis
					- efficiency report summary / feedback mode
					- report toolbar / feedback toolbar
            */}
			<div
				css={css`
					display: flex;
					flex-direction: column;
					flex: 1.1;
					gap: 10px;
				`}
			>
				{state.analyze ? (
					farmAnalysis
				) : (
					<>
						{sowFarmLocationsAndPeriodLengthResponse.isLoading && loadingSpin}
						{sowFarmLocationsAndPeriodLengthResponse.error !== null && errorMessage(sowFarmLocationsAndPeriodLengthResponse.error.message)}
						{sowFarmLocationsAndPeriodLengthLoadedWithoutError && sowFarmLocationsAndPeriodLength && (
							<FarmLocationSelector
								sowFarmLocations={sowFarmLocationsAndPeriodLength.sowFarmLocations}
								selectedFarmLocationId={state.selectedFarmLocationId}
								onSelectedFarmLocationId={(farmLocationId) => {
									state.setSelectedFarmLocation(farmLocationId);
								}}
							/>
						)}
						{sowFarmLocationsAndPeriodLengthLoadedWithoutError && sowFarmLocationsAndPeriodLength && !state.analyze && (
							<PeriodSelector
								periodLength={periodLength}
								periodType={state.periodType}
								periodSinceDate={state.periodSinceDate}
								periodSinceMonth={state.periodSinceMonth}
								onPeriodTypeChange={(periodType) => state.setPeriodType(periodType)}
								onPeriodSinceChange={(periodSince) => state.setPeriodSince(periodSince)}
								dateFormat={props.dateFormat}
							/>
						)}
						{analyzeButton}
					</>
				)}
			</div>
			<div
				css={css`
					border-left: 1px solid #ddd;
					margin: 10px 0;
				`}
			/>
			{/* Right panel with PDF preview or analysis data */}
			<div
				css={css`
					display: flex;
					flex-direction: column;
					flex: 1;
					overflow: hidden;
				`}
			>
				{state.pdfRendererState ? (
					<PDFRenderer jwtToken={props.jwtToken} state={state.pdfRendererState} onClose={() => state.clearPdfContent()} />
				) : (
					analysisData && (
						<div
							css={css`
								padding: 0 25px;
								overflow: auto;
							`}
							key={analysisData.index}
						>
							<h3>{`Relevant data: ${analysisData.title}`}</h3>
							<PigbotMarkdown>{removeHeading(analysisData.relevantData)}</PigbotMarkdown>
						</div>
					)
				)}
			</div>
		</div>
	);
});
